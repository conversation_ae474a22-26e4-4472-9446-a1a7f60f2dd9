// Fully Responsive Chakra UI Dashboard
import React, { useState, useEffect } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  IconButton,
  Spinner,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Image,
  useToast,
  Stack,
  Badge,
  VStack,
  HStack,
  Icon,
} from "@chakra-ui/react";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  RepeatIcon,
} from "@chakra-ui/icons";
import { CalendarIcon } from "@chakra-ui/icons";
import { MdLocationOn, MdGroups } from "react-icons/md";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import "./CalendarStyle.css";
import {
  convertDateIntoIndianFormat,
  convertTime,
} from "../../utilities/dateHelper.js";
import moment from "moment-timezone";
import axios from "axios";
import { Select } from "@chakra-ui/react";
import { Skeleton, SkeletonText } from "@chakra-ui/react";
import { useNavigate } from "react-router-dom";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

// SVG Placeholder Component for undefined images
const PlaceholderIcon = ({ size = "70px" }) => (
  <Box
    width={size}
    height={size}
    bg="gray.100"
    borderRadius="md"
    display="flex"
    alignItems="center"
    justifyContent="center"
    color="gray.400"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="32"
      height="32"
      fill="currentColor"
      viewBox="0 0 1200 1200"
    >
      <path d="M1084.171,972.733c-2.193-14.526-8.187-27.639-15.192-40.283c-7.787-14.057-19.176-24.394-34.111-30.639  c-1.052-0.439-2.094-0.9-3.135-1.361c-8.568-3.789-15.438-9.535-20.518-17.461c-3.278-5.115-6.662-10.172-10.215-15.101  c-6.617-9.18-15.141-15.918-25.984-19.509c-15.946-5.281-19.641-4.247-30.424,8.861c-4.636,5.635-9.968,10.333-16.262,14.084  c-4.551,2.713-8.803,5.944-13.089,9.081c-2.497,1.829-5.01,4.226-4.671,7.424c0.66,6.231-3.551,7.837-7.932,9.404  c-3.567,1.276-7.195,2.453-10.879,3.321c-20.401,4.809-41.053,7.493-62.054,7.596c-13.293,0.065-26.587,0.997-39.875,0.884  c-16.004-0.135-30.633,4.49-44.919,11.088c-11.06,5.109-21.553,11.209-32.028,17.373c-3.865,2.275-7.501,5.156-12.645,5.625  c-2.756-9.759-2.849-19.603-3.467-29.391c-0.234-3.701,0.559-6.654,3.931-8.724c3.498-2.147,3.731-5.447,2.601-9.017  c-0.459-1.447-1.058-2.848-1.544-4.287c-4.628-13.706-7.972-27.686-9.648-42.092c-1.405-12.065-2.883-24.151-5.084-36.088  c-3.032-16.437-5.781-32.901-7.891-49.481c-0.862-6.775-1.848-13.546-3.118-20.255c-1.78-9.405-5.814-18.026-9.77-26.669  c-2.445-5.341-2.314-5.402,2.319-9.407c6.96-6.017,7.054-6.533,4.101-15.633c-2.812-8.665-5.583-17.344-8.258-26.052  c-4.122-13.42-4.244-26.78-0.261-40.381c3.988-13.615,9.788-26.314,17.193-38.324c5.581-9.052,11.711-17.783,16.982-27.009  c8.273-14.48,19.103-27.508,24.755-43.489c1.09-3.083,3.734-4.887,6.764-5.618c3.297-0.794,6.724-1.373,10.105-1.435  c11.013-0.202,22.036-0.317,33.048-0.105c12.532,0.242,25.063,0.759,37.583,1.376c7.957,0.393,14.298-1.605,17.198-9.849  c0.965-2.741,2.982-4.286,5.727-5.033c2.925-0.796,5.812-1.739,8.753-2.468c13.628-3.379,27.128-7.582,40.945-9.806  c14.049-2.261,25.543-8.653,36.785-16.734c12.551-9.021,23.026-19.82,31.208-32.801c5.867-9.31,11.449-18.827,16.645-28.526  c6.465-12.069,13.499-23.775,20.813-35.333c3.275-5.175,7.211-9.776,11.575-14.031c1.626-1.586,3.237-3.343,5.675-3.553  c9.201-0.792,13.164-8.186,18.049-14.343c1.758-2.216,2.3-5.006,0.311-7.359c-2.66-3.148-3.345-6.735-3.162-10.621  c0.399-8.539-1.376-15.93-10.102-19.807c-0.32-0.142-0.591-0.496-0.782-0.814c-4.173-6.959-12.256-8.254-18.417-12.269  c-2.629-1.714-4.663-2.692-5.862-5.878c-2.51-6.674-6.013-7.704-12.335-4.385c-0.997,0.523-1.842,1.335-2.76,2.009  c-1.776,1.306-3.545,1.085-5.215-0.147c-1.832-1.353-3.775-2.623-5.357-4.235c-12.886-13.135-28-23.274-43.696-32.625  c-2.981-1.776-5.482-3.999-7.866-6.488c-13.406-13.99-26.847-27.946-40.357-41.834c-8.543-8.783-17.246-17.41-25.808-26.175  c-0.463-0.474-0.934-0.941-1.396-1.416c-0.144-0.148-0.247-0.259-0.379-0.397c-3.354-3.468-3.423-3.968-0.315-8.133  c0.007-0.009,0.01-0.014,0.017-0.023c0.509-0.682,0.985-1.37,1.437-2.062c3.267-5.003,5.065-10.28,5.769-15.752  c0.131-1.025,0.225-2.057,0.282-3.095c0.061-1.12,0.082-2.248,0.062-3.383c-0.021-1.208-0.089-2.425-0.196-3.648  c-0.163-1.882-0.411-3.778-0.754-5.69c-0.696-3.883-1.703-7.817-3.165-11.499c-0.207-0.52-0.43-1.03-0.656-1.539  c-0.16-0.359-0.293-0.733-0.463-1.085c-0.232-0.482-0.488-0.936-0.725-1.413c-0.269-0.541-0.548-1.068-0.822-1.601  c-2.312-4.501-4.808-8.766-7.508-12.773c-0.489-0.725-0.98-1.447-1.482-2.156c-3.126-4.417-6.508-8.508-10.138-12.28  c-0.588-0.611-1.173-1.224-1.773-1.818c-3.746-3.701-7.762-7.057-12.03-10.084c-0.691-0.49-1.379-0.985-2.085-1.457  c-4.399-2.948-9.077-5.534-14.019-7.776c-0.801-0.363-1.602-0.727-2.417-1.071c-3.264-1.379-6.641-2.609-10.135-3.685  c-1.868-0.575-3.776-1.097-5.71-1.585c-0.091-0.023-0.18-0.05-0.272-0.073c-0.917-0.229-1.843-0.446-2.776-0.655  c-1.697-0.381-3.398-0.757-5.144-1.071c-4.79-0.863-9.604-0.922-14.397-0.299c-1.088,0.141-2.174,0.326-3.259,0.537  c-0.221,0.043-0.443,0.092-0.665,0.138c-0.803,0.167-1.605,0.35-2.406,0.553c-0.612,0.156-1.226,0.305-1.838,0.482  c-5.23,1.511-9.821,3.844-13.674,6.869c-0.675,0.53-1.326,1.082-1.954,1.654c-3.943,3.586-6.948,7.995-8.912,13.042  c-0.326,0.839-0.624,1.695-0.891,2.57c-1.043,3.414-1.654,7.075-1.743,10.967c-0.002,0.1,0.007,0.198,0.005,0.298  c-0.023,1.28-0.001,2.548,0.059,3.806c0.073,1.53,0.201,3.046,0.391,4.545c0.166,1.31,0.389,2.605,0.642,3.891  c0.72,3.673,1.795,7.247,3.162,10.733c0.346,0.882,0.684,1.766,1.068,2.636c1.157,2.622,2.479,5.193,3.961,7.709  c1.37,2.327,2.818,4.556,4.311,6.733c0.493,0.72,0.982,1.445,1.488,2.148c2.402,3.335,4.965,6.471,7.665,9.439  c0.843,0.926,1.698,1.837,2.57,2.728c0.592,0.605,1.183,1.212,1.788,1.801c3.77,3.665,7.795,7.01,12.072,10.037  c0.692,0.49,1.398,0.962,2.103,1.435c0.038,0.026,0.076,0.052,0.114,0.078c4.358,2.915,8.954,5.528,13.809,7.81  c0.794,0.373,1.608,0.719,2.414,1.075c3.796,1.675,7.72,3.187,11.804,4.489c1.349,0.43,2.701,0.802,4.055,1.152  c0.929,0.241,1.859,0.439,2.788,0.641c2.731,0.591,5.466,1.024,8.205,1.246c1.37,0.111,2.742,0.195,4.112,0.206  c2.542,0.02,5.082-0.184,7.618-0.536c1.321-0.183,2.641-0.426,3.959-0.718c2.431-0.538,4.857-1.204,7.27-2.133  c0.322-0.124,0.615-0.191,0.925-0.291c1.086-0.353,2.12-0.6,3.084-0.681c3.434-0.29,6.183,1.104,8.913,3.724  c0.358,0.343,0.714,0.681,1.073,1.066c0.233,0.25,0.476,0.49,0.71,0.738c6.267,6.659,12.955,12.927,19.482,19.343  c17.607,17.309,36.131,33.738,52.264,52.473c14.621,16.98,28.324,34.758,43.946,50.867c3.975,4.099,4.68,8.207,2.429,13.409  c-3.08,7.115-4.317,14.45-1.671,22.157c1.854,5.402,0.268,10.479-2.654,15.198c-6.868,11.092-15.144,20.678-26.158,28.106  c-17.925,12.09-36.514,23.351-52.064,38.672c-6.358,6.264-14.423,8.24-22.98,8.382c-19.377,0.322-38.751,0.105-58.108-1.024  c-9.238-0.539-17.927-2.798-26.145-7.169c-5.354-2.848-10.873-5.489-16.546-7.617c-12.159-4.562-24.591-7.688-37.816-5.856  c-5.438,0.753-10.208-2.252-10.796-7.569c-0.485-4.385-2.318-7.895-4.072-11.65c-0.953-2.04-1.97-4.305-1.989-6.477  c-0.055-6.132-2.945-10.815-6.939-14.741c1.417-3.487,4.203-4.979,6.654-6.65c8.551-5.827,13.744-13.947,17.018-23.577  c1.848-5.437,3.129-10.961,3.502-16.672c0.454-6.937,1.979-13.503,5.871-19.393c3.297-4.989,4.003-10.574,3.557-16.389  c-0.712-9.277-3.483-17.805-8.923-25.456c-4.209-5.921-9.875-10.58-14.223-16.28c-8.648-11.335-20.374-16.592-33.842-19.118  c-7.456-1.398-14.963-2.529-22.193-5.016c-5.519-1.898-11.243-1.755-16.881-0.449c-4.435,1.028-8.764,2.531-13.211,3.49  c-9.113,1.966-15.592,7.689-20.95,14.762c-3.704,4.89-7.1,10.721-5.74,16.821c1.646,7.385-1.369,12.384-5.441,17.533  c-1.884,2.383-3.462,5.017-5.441,7.313c-5.081,5.896-7.278,12.708-5.007,20.102c2.559,8.332-0.812,13.858-6.805,18.849  c-3.485,2.902-7.46,5.504-9.423,9.756c-1.564,3.389-4.114,4.089-7.354,4.07c-5.659-0.034-11.344-0.68-16.594,2.658  c-1.223,0.778-3.739,0.114-5.424-0.518c-13.285-4.982-27.2-6.831-41.144-8.642c-19.24-2.497-38.376-5.439-55.869-14.646  c-2.279-1.2-4.532-0.854-6.142,1.166c-2.791,3.503-5.927,2.671-9.139,0.96c-2.678-1.427-5.28-3.001-7.886-4.56  c-8.147-4.873-16.136-10.031-24.456-14.589c-9.317-5.106-18.78-10.07-29.167-12.728c-12.501-3.2-25.025-6.331-37.614-9.163  c-19.71-4.433-38.804-10.509-56.637-20.162c-5.74-3.107-11.695-5.402-18.301-5.819c-4.664-0.294-8.776-2.305-12.741-4.621  c-8.853-5.172-17.793-10.206-26.529-15.569c-10.093-6.194-20.612-11.5-31.998-14.689c-7.422-2.079-14.06-5.561-20.817-9.03  c-5.728-2.941-11.658-5.554-17.679-7.84c-4.328-1.644-8.903-1.839-13.217,0.613c-3.183,1.809-4.086,5.044-1.663,7.515  c2.37,2.417,5.032,4.603,7.795,6.567c4.021,2.857,8.313,5.332,12.365,8.146c3.114,2.162,3.222,3.634,0.331,6.21  c-5.522,4.922-10.409,9.982-9.605,18.315c0.156,1.622-1.453,3.398-2.189,5.127c-0.881,2.073-1.912,4.117-2.479,6.279  c-0.498,1.9-0.037,4.474,1.561,5.16c6.571,2.821,11.553,8.735,19.173,9.512c10.979,1.119,21.631,3.994,32.393,6.299  c12.635,2.706,25.417,5.113,38.379,4.196c8.007-0.567,15.014,1.448,21.763,5.167c8.325,4.588,16.366,9.644,23.863,15.51  c14.369,11.241,29.197,21.839,44.347,32.001c21.588,14.478,44.17,26.642,69.948,32.067c10.208,2.148,17.411,8.64,22.388,17.802  c4.347,8.004,9.128,15.773,13.511,23.759c3.012,5.488,7.256,8.548,13.658,8.506c6.269-0.041,11.623,2.575,17.119,5.424  c10.952,5.679,19.829,13.96,29.28,21.584c5.459,4.403,8.588,9.178,6.732,16.509c-0.63,2.488-0.15,5.301,0.055,7.948  c1.588,20.519-0.09,41.007-0.542,61.505c-0.058,2.65-0.307,5.32-0.741,7.935c-2.706,16.276-1.96,32.566-0.231,48.849  c0.842,7.928,2.062,15.815,2.956,23.738c1.628,14.417-0.22,28.033-9.279,40.03c-3.2,4.237-5.954,8.807-9.055,13.124  c-3.323,4.626-6.826,9.122-10.225,13.694c-5.667,7.622-5.89,8.777,0.382,15.542c15.545,16.764,31.484,33.091,54.378,39.901  c2.162,0.643,2.885,2.371,2.223,4.468c-1.026,3.255-1.982,6.562-3.371,9.666c-3.413,7.623-7.343,15.025-10.525,22.737  c-3.306,8.011-8.627,14.105-15.373,19.35c-10.192,7.926-20.163,16.14-30.176,24.295c-2.647,2.155-5.17,4.475-7.626,6.847  c-2.536,2.45-4.452,5.372-3.928,9.066c0.604,4.256-1.397,7.239-4.042,10.133c-2.819,3.084-5.446,6.342-8.249,9.442  c-12.491,13.816-24.172,28.3-35.826,42.82c-6.492,8.089-9.944,16.863-9.223,27.299c0.366,5.299,0.558,10.649,0.269,15.945  c-0.408,7.452,2.31,13.394,7.78,18.138c8.319,7.214,16.567,14.534,25.228,21.32c7.564,5.928,16.357,9.718,25.863,11.312  c11.316,1.898,22.045,5.632,32.743,9.559c3.919,1.439,7.786,3.078,11.799,4.193c22.311,6.197,44.643,12.325,67.001,18.35  c19.442,5.24,38.646,11.219,57.536,18.18c5.242,1.931,10.582,3.881,14.366,8.316c-7.125,10.173-6.863,16.768,0.946,25.668  c1.741,1.984,3.853,3.649,5.845,5.405c5.414,4.775,10.928,9.438,16.271,14.292c15.475,14.063,27.594,30.28,32.543,51.077  c0.702,2.944,1.509,5.922,2.758,8.661c1.243,2.724,2.722,5.532,4.766,7.663c5.955,6.207,16.318,9.735,24.255-1.454  c1.745-2.461,3.374-5.127,4.425-7.934c2.927-7.815,6.043-15.619,8.138-23.673c4.262-16.388,5.598-32.847-0.018-49.4  c-2.552-7.521-3.962-15.431-5.885-23.166c-1.248-5.018-0.955-5.954,3.564-7.893c11.512-4.939,23.157-9.573,34.616-14.628  c13.562-5.983,27.483-11.003,41.35-16.199c17.061-6.393,33.985-13.155,50.945-19.817c14.837-5.828,29.516-12.084,44.5-17.499  c11.79-4.26,24.051-6.958,36.622-8.016c3.893-0.328,6.671,0.754,8.04,4.62c3.345,9.451,10.593,12.248,19.847,11.549  c2.271-0.171,4.552-0.331,6.8-0.679c7.264-1.124,14.451-0.37,21.486,1.303c9.601,2.284,19.014,5.23,28.299,8.658  c10.88,4.016,20.625,9.567,28.531,18.173c1.533,1.668,3.498,2.932,5.094,4.55c5.087,5.156,11.292,6.08,17.912,4.425  c4.016-1.005,7.953-2.548,11.712-4.308c6.387-2.991,9.666-8.354,10.306-15.294C1085.108,982.569,1084.915,977.659,1084.171,972.733z"/>
    </svg>
  </Box>
);

const Dashboard = () => {
  const [selectedDates, setSelectedDates] = useState([new Date()]);
  const [allBookings, setAllBookings] = useState([]);
  const [selectedBooking, setSelectedBooking] = useState({});
  const [loading, setLoading] = useState(true);
  const toast = useToast();

  const [coaches, setCoaches] = useState([]);
  const [selectedCoach, setSelectedCoach] = useState();

  const token = sessionStorage.getItem("admintoken")?.split(" ")[1] || "";
  const navigate = useNavigate();
  const handleDay = (action) => {
    const current = selectedDates[0];
    if (action === "prev") current.setDate(current.getDate() - 1);
    if (action === "next") current.setDate(current.getDate() + 1);
    if (action === "today") setSelectedDates([new Date()]);
    else setSelectedDates([new Date(current)]);
  };

  const handleDateChange = (date) => {
    setSelectedDates([date]);
  };

  const getLatestBookings = async () => {
    try {
      setLoading(true);

      const date = moment(selectedDates[0]).format("YYYY-MM-DD");

      // Prepare request body - only include coachId if it's not empty/undefined
      const requestBody = { date };
      if (selectedCoach && selectedCoach !== "all") {
        requestBody.coachId = selectedCoach;
      }

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy/dashboard`,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = response.data;

      console.log(result, "coach result here");

      if (result?.responseCode === 0 && result?.status === "success") {
        setAllBookings(result.data);
        setSelectedBooking(result.data?.[0] || {});
      } else {
        toast({ title: "Failed to fetch coaches", status: "error" });
      }

      setLoading(false);
    } catch (err) {
      console.error(err);
      toast({ title: "Failed to load bookings", status: "error" });
      setLoading(false);
    }
  };

  const fetchCoachData = async () => {
    try {
      const response = await axios({
        method: "get",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/coach`,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setCoaches(response.data.data);

      console.log("Coach Data:", response);
      return response.data;
    } catch (error) {
      console.error("Error fetching coach data:", error);
      throw error;
    }
  };

  useEffect(() => {
    fetchCoachData();
  }, []);

  useEffect(() => {
    getLatestBookings();
  }, [selectedDates, selectedCoach]);

  return (
    <Layout title="Dashboard">
      {/* Page Heading and Back Button */}
      <Flex alignItems="center" gap={0} mb={3} >
        <Button
          variant="ghost"
          size={{ base: "xs", md: "sm" }}
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          onClick={() => navigate(-1)}
          _hover={{ bg: "gray.100" }}
          color="gray.700"
          fontWeight="bold"
        >
        </Button>
        <Heading as="h1" fontSize={{ base: "md", md: "lg" }} color="gray.700" fontWeight="medium">
          Dashboard
        </Heading>
      </Flex>
      <Box 
        px={{ base: 0, md: 4, lg: 6 }}
        py={{ base: 2, md: 4 }}
        maxW="100%"
        overflow="hidden"
      >
        {/* Header Controls - Responsive */}
        <Flex 
          py={{ base: 2, md: 4 }}
          justifyContent="space-between"
          align="center"
          direction={{ base: "column", md: "row" }}
          gap={{ base: 3, md: 0 }}
        >
          <Flex 
            align="center" 
            gap={{ base: 2, md: 4 }}
            wrap="wrap"
            justify={{ base: "center", md: "flex-start" }}
          >
            <IconButton
              icon={<ChevronLeftIcon />}
              aria-label="Previous day"
              onClick={() => handleDay("prev")}
              size={{ base: "sm", md: "md" }}
            />
            <IconButton
              icon={<ChevronRightIcon />}
              aria-label="Next day"
              onClick={() => handleDay("next")}
              size={{ base: "sm", md: "md" }}
            />
            <Button 
              onClick={() => handleDay("today")}
              size={{ base: "sm", md: "md" }}
            >
              Today
            </Button>
            <Text 
              fontWeight="semibold"
              fontSize={{ base: "sm", md: "md", lg: "lg" }}
              textAlign={{ base: "center", md: "left" }}
            >
              {`${selectedDates[0].getDate()} ${
                months[selectedDates[0].getMonth()]
              } ${selectedDates[0].getFullYear()}`}
            </Text>
          </Flex>
        </Flex>

        {/* Main Content - Responsive Layout */}
        <Flex 
          direction={{ base: "column", lg: "row" }} 
          gap={{ base: 4, md: 6 }}
          align="stretch"
        >
          {/* Left Panel - Calendar & Bookings */}
          <Box 
            flex={{ base: "1", lg: "0 0 45%" }}
            bg="white" 
            borderRadius="md" 
            boxShadow="md"
            p={{ base: 3, md: 4 }}
            minH={{ base: "auto", lg: "80vh" }}
          >
            {/* Calendar Container */}
            <Box 
              mb={{ base: 4, md: 6 }}
              sx={{
                '.react-calendar': {
                  width: '100%',
                  border: 'none',
                  fontSize: { base: '12px', md: '14px', lg: '16px' },
                },
                '.react-calendar__tile': {
                  height: { base: '40px', md: '55px', lg: '70px' },
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                },
                '.react-calendar__month-view__weekdays': {
                  fontSize: { base: '10px', md: '12px', lg: '14px' },
                  height: { base: '35px', md: '45px', lg: '55px' },
                },
                '.react-calendar__navigation': {
                  height: { base: '40px', md: '55px', lg: '70px' },
                  marginBottom: { base: '0.5rem', md: '1rem' },
                },
                '.react-calendar__navigation button': {
                  fontSize: { base: '14px', md: '16px', lg: '18px' },
                  minWidth: { base: '30px', md: '40px', lg: '50px' },
                }
              }}
            >
              <Calendar
                value={selectedDates}
                onChange={handleDateChange}
                tileClassName={() => "chakra-calendar-tile"}
              />
            </Box>

            {/* Upcoming Section Header */}
            <Flex
              direction="row"
              align="center"
              justify="space-between"
              mb={{ base: 3, md: 4 }}
              gap={{ base: 2, md: 0 }}
            >
              <Text 
                fontSize={{ base: "md", md: "lg", lg: "xl" }} 
                fontWeight="bold"
              >
                Upcoming
              </Text>
              <Select
                placeholder="Select Coach"
                maxW={{ base: "160px", md: "200px" }}
                size={{ base: "sm", md: "md" }}
                value={selectedCoach || ""}
                onChange={(x) => setSelectedCoach(x.target.value)}
              >
                <option value="" disabled>Select Coach</option>
                <option value="all">All</option>
                {coaches &&
                  coaches.length > 0 &&
                  coaches.map((x) => {
                    return (
                      <option key={x._id} value={x._id}>
                        {x.firstName}
                      </option>
                    );
                  })}
              </Select>
            </Flex>

            {/* Bookings List */}
            <Box 
              maxH={{ base: "40vh", md: "50vh", lg: "52vh" }} 
              overflowY="auto"
              css={{
                '&::-webkit-scrollbar': {
                  width: '4px',
                },
                '&::-webkit-scrollbar-track': {
                  background: '#f1f1f1',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: '#888',
                  borderRadius: '4px',
                },
              }}
            >
              {allBookings.length > 0 ? (
                <VStack spacing={{ base: 2, md: 3 }} align="stretch">
                  {allBookings.map((booking, idx) => (
                    <Box
                      key={idx}
                      borderWidth="1px"
                      borderRadius="md"
                      overflow="hidden"
                      onClick={() => setSelectedBooking(booking)}
                      bg={booking === selectedBooking ? "blue.50" : "white"}
                      p={{ base: 3, md: 4 }}
                      cursor="pointer"
                      _hover={{ bg: "gray.50" }}
                      transition="background-color 0.2s"
                    >
                      <VStack align="stretch" spacing={2}>
                        <Text 
                          fontWeight="medium"
                          fontSize={{ base: "sm", md: "md" }}
                          noOfLines={2}
                        >
                          {booking.name.length > 50
                            ? `${booking.name.slice(0, 50)}...`
                            : booking.name}
                        </Text>
                        <HStack
                          fontSize={{ base: "xs", md: "sm" }}
                          color="gray.500"
                          spacing={1}
                        >
                          <CalendarIcon />
                          <Text>
                            {convertDateIntoIndianFormat(selectedDates[0])}
                            &nbsp; at &nbsp;
                            {`${convertTime(
                              booking?.startTime
                            )} - ${convertTime(booking?.endTime)}`}
                          </Text>
                        </HStack>
                        <HStack justify="flex-end" spacing={2}>
                          {booking.type === "class" && (
                            <Badge colorScheme="green" size={{ base: "sm", md: "md" }}>
                              Class
                            </Badge>
                          )}
                          {booking.type === "course" && (
                            <Badge colorScheme="yellow" size={{ base: "sm", md: "md" }}>
                              Course
                            </Badge>
                          )}
                          {booking.type === "event" && (
                            <Badge colorScheme="blue" size={{ base: "sm", md: "md" }}>
                              Break
                            </Badge>
                          )}
                        </HStack>
                      </VStack>
                    </Box>
                  ))}
                </VStack>
              ) : (
                <Text 
                  textAlign="center" 
                  color="gray.500"
                  py={{ base: 4, md: 8 }}
                  fontSize={{ base: "sm", md: "md" }}
                >
                  No Bookings Found
                </Text>
              )}
            </Box>
          </Box>

          {/* Right Panel - Booking Details */}
          <Box 
            flex={{ base: "1", lg: "0 0 50%" }}
            bg="white" 
            p={{ base: 4, md: 6 }} 
            borderRadius="md" 
            boxShadow="sm"
            minH={{ base: "300px", lg: "80vh" }}
          >
            {loading ? (
              <Stack spacing={4} py={{ base: 4, md: 10 }}>
                <Skeleton height="60px" borderRadius="md" />
                <SkeletonText noOfLines={3} spacing="4" skeletonHeight="2" />
                <Skeleton height="40px" borderRadius="md" />
                <SkeletonText noOfLines={2} spacing="4" skeletonHeight="2" />
              </Stack>
            ) : selectedBooking.name ? (
              <Stack spacing={{ base: 3, md: 4 }}>
                <Flex 
                  justify="space-between" 
                  align="flex-start"
                  direction={{ base: "column", md: "row" }}
                  gap={{ base: 3, md: 0 }}
                >
                  {/* Left - Image + Info */}
                  <HStack
                    spacing={{ base: 3, md: 4 }}
                    align="flex-start"
                    flex="1"
                  >
                    {selectedBooking.image ? (
                      <Image
                        boxSize={{ base: "50px", md: "70px" }}
                        objectFit="cover"
                        borderRadius="md"
                        src={selectedBooking.image}
                        alt="Course"
                        fallback={<PlaceholderIcon size={{ base: "50px", md: "70px" }} />}
                      />
                    ) : (
                      <PlaceholderIcon size={{ base: "50px", md: "70px" }} />
                    )}
                    <VStack align="flex-start" spacing={1} flex="1">
                      <Text 
                        fontWeight="semibold" 
                        fontSize={{ base: "sm", md: "md" }}
                        noOfLines={2}
                      >
                        {selectedBooking.name}
                      </Text>
                      <Text 
                        fontWeight="medium" 
                        color="blue.600" 
                        fontSize={{ base: "xs", md: "sm" }}
                      >
                        {convertTime(selectedBooking.startTime)} -{" "}
                        {convertTime(selectedBooking.endTime)}
                      </Text>
                      <HStack
                        color="gray.600"
                        fontSize={{ base: "xs", md: "sm" }}
                        spacing={1}
                      >
                        <MdLocationOn />
                        <Text>{selectedBooking.facility}</Text>
                      </HStack>
                    </VStack>
                  </HStack>

                  {/* Right - Date, Count, Refresh */}
                  <VStack 
                    textAlign="right" 
                    fontSize={{ base: "xs", md: "sm" }}
                    spacing={2}
                    align={{ base: "stretch", md: "flex-end" }}
                  >
                    <Button
                      size={{ base: "xs", md: "sm" }}
                      variant="ghost"
                      colorScheme="green"
                      leftIcon={<RepeatIcon />}
                      onClick={getLatestBookings}
                    >
                      Refresh
                    </Button>
                    <Text>
                      {convertDateIntoIndianFormat(selectedDates[0])}
                    </Text>
                    <Text color="gray.500">
                      No. of Bookings: {selectedBooking?.bookings?.length || 0}
                    </Text>
                  </VStack>
                </Flex>

                {/* No bookings message */}
                <Box
                  borderTop="1px solid"
                  borderColor="gray.100"
                  pt={{ base: 2, md: 3 }}
                  textAlign="left"
                  fontSize={{ base: "xs", md: "sm" }}
                  color="gray.600"
                >
                  No Bookings Found
                </Box>
              </Stack>
            ) : (
              <Text 
                textAlign="center" 
                color="gray.500"
                py={{ base: 4, md: 8 }}
                fontSize={{ base: "sm", md: "md" }}
              >
                No Booking Selected
              </Text>
            )}
          </Box>
        </Flex>
      </Box>
    </Layout>
  );
};

export default Dashboard;