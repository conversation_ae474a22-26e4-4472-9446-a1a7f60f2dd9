// Fully Responsive Chakra UI Dashboard
import React, { useState, useEffect } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  IconButton,
  Spinner,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Image,
  useToast,
  Stack,
  Badge,
  VStack,
  HStack,
} from "@chakra-ui/react";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  RepeatIcon,
} from "@chakra-ui/icons";
import { CalendarIcon } from "@chakra-ui/icons";
import { MdLocationOn, MdGroups } from "react-icons/md";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import "./CalendarStyle.css";
import {
  convertDateIntoIndianFormat,
  convertTime,
} from "../../utilities/dateHelper.js";
import moment from "moment-timezone";
import axios from "axios";
import { Select } from "@chakra-ui/react";
import { Skeleton, SkeletonText } from "@chakra-ui/react";
import { useNavigate } from "react-router-dom";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const Dashboard = () => {
  const [selectedDates, setSelectedDates] = useState([new Date()]);
  const [allBookings, setAllBookings] = useState([]);
  const [selectedBooking, setSelectedBooking] = useState({});
  const [loading, setLoading] = useState(true);
  const toast = useToast();

  const [coaches, setCoaches] = useState([]);
  const [selectedCoach, setSelectedCoach] = useState();

  const token = sessionStorage.getItem("admintoken")?.split(" ")[1] || "";
  const navigate = useNavigate();
  const handleDay = (action) => {
    const current = selectedDates[0];
    if (action === "prev") current.setDate(current.getDate() - 1);
    if (action === "next") current.setDate(current.getDate() + 1);
    if (action === "today") setSelectedDates([new Date()]);
    else setSelectedDates([new Date(current)]);
  };

  const handleDateChange = (date) => {
    setSelectedDates([date]);
  };

  const getLatestBookings = async () => {
    try {
      setLoading(true);

      const date = moment(selectedDates[0]).format("YYYY-MM-DD");

      // Prepare request body - only include coachId if it's not empty/undefined
      const requestBody = { date };
      if (selectedCoach && selectedCoach !== "all") {
        requestBody.coachId = selectedCoach;
      }

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy/dashboard`,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = response.data;

      console.log(result, "coach result here");

      if (result?.responseCode === 0 && result?.status === "success") {
        setAllBookings(result.data);
        setSelectedBooking(result.data?.[0] || {});
      } else {
        toast({ title: "Failed to fetch coaches", status: "error" });
      }

      setLoading(false);
    } catch (err) {
      console.error(err);
      toast({ title: "Failed to load bookings", status: "error" });
      setLoading(false);
    }
  };

  const fetchCoachData = async () => {
    try {
      const response = await axios({
        method: "get",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/coach`,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setCoaches(response.data.data);

      console.log("Coach Data:", response);
      return response.data;
    } catch (error) {
      console.error("Error fetching coach data:", error);
      throw error;
    }
  };

  useEffect(() => {
    fetchCoachData();
  }, []);

  useEffect(() => {
    getLatestBookings();
  }, [selectedDates, selectedCoach]);

  return (
    <Layout title="Dashboard">
      {/* Page Heading and Back Button */}
      <Flex alignItems="center" gap={0} mb={3} >
        <Button
          variant="ghost"
          size={{ base: "xs", md: "sm" }}
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          onClick={() => navigate(-1)}
          _hover={{ bg: "gray.100" }}
          color="gray.700"
          fontWeight="bold"
        >
        </Button>
        <Heading as="h1" fontSize={{ base: "md", md: "lg" }} color="gray.700" fontWeight="medium">
          Dashboard
        </Heading>
      </Flex>
      <Box 
        px={{ base: 0, md: 4, lg: 6 }}
        py={{ base: 2, md: 4 }}
        maxW="100%"
        overflow="hidden"
      >
        {/* Header Controls - Responsive */}
        <Flex 
          py={{ base: 2, md: 4 }}
          justifyContent="space-between"
          align="center"
          direction={{ base: "column", md: "row" }}
          gap={{ base: 3, md: 0 }}
        >
          <Flex 
            align="center" 
            gap={{ base: 2, md: 4 }}
            wrap="wrap"
            justify={{ base: "center", md: "flex-start" }}
          >
            <IconButton
              icon={<ChevronLeftIcon />}
              aria-label="Previous day"
              onClick={() => handleDay("prev")}
              size={{ base: "sm", md: "md" }}
            />
            <IconButton
              icon={<ChevronRightIcon />}
              aria-label="Next day"
              onClick={() => handleDay("next")}
              size={{ base: "sm", md: "md" }}
            />
            <Button 
              onClick={() => handleDay("today")}
              size={{ base: "sm", md: "md" }}
            >
              Today
            </Button>
            <Text 
              fontWeight="semibold"
              fontSize={{ base: "sm", md: "md", lg: "lg" }}
              textAlign={{ base: "center", md: "left" }}
            >
              {`${selectedDates[0].getDate()} ${
                months[selectedDates[0].getMonth()]
              } ${selectedDates[0].getFullYear()}`}
            </Text>
          </Flex>
        </Flex>

        {/* Main Content - Responsive Layout */}
        <Flex 
          direction={{ base: "column", lg: "row" }} 
          gap={{ base: 4, md: 6 }}
          align="stretch"
        >
          {/* Left Panel - Calendar & Bookings */}
          <Box 
            flex={{ base: "1", lg: "0 0 45%" }}
            bg="white" 
            borderRadius="md" 
            boxShadow="md"
            p={{ base: 3, md: 4 }}
            minH={{ base: "auto", lg: "80vh" }}
          >
            {/* Calendar Container */}
            <Box 
              mb={{ base: 4, md: 6 }}
              sx={{
                '.react-calendar': {
                  width: '100%',
                  border: 'none',
                  fontSize: { base: '12px', md: '14px', lg: '16px' },
                },
                '.react-calendar__tile': {
                  height: { base: '40px', md: '55px', lg: '70px' },
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                },
                '.react-calendar__month-view__weekdays': {
                  fontSize: { base: '10px', md: '12px', lg: '14px' },
                  height: { base: '35px', md: '45px', lg: '55px' },
                },
                '.react-calendar__navigation': {
                  height: { base: '40px', md: '55px', lg: '70px' },
                  marginBottom: { base: '0.5rem', md: '1rem' },
                },
                '.react-calendar__navigation button': {
                  fontSize: { base: '14px', md: '16px', lg: '18px' },
                  minWidth: { base: '30px', md: '40px', lg: '50px' },
                }
              }}
            >
              <Calendar
                value={selectedDates}
                onChange={handleDateChange}
                tileClassName={() => "chakra-calendar-tile"}
              />
            </Box>

            {/* Upcoming Section Header */}
            <Flex
              direction="row"
              align="center"
              justify="space-between"
              mb={{ base: 3, md: 4 }}
              gap={{ base: 2, md: 0 }}
            >
              <Text 
                fontSize={{ base: "md", md: "lg", lg: "xl" }} 
                fontWeight="bold"
              >
                Upcoming
              </Text>
              <Select
                placeholder="Select Coach"
                maxW={{ base: "160px", md: "200px" }}
                size={{ base: "sm", md: "md" }}
                value={selectedCoach || ""}
                onChange={(x) => setSelectedCoach(x.target.value)}
              >
                <option value="all">All</option>
                {coaches &&
                  coaches.length > 0 &&
                  coaches.map((x) => {
                    return (
                      <option key={x._id} value={x._id}>
                        {x.firstName}
                      </option>
                    );
                  })}
              </Select>
            </Flex>

            {/* Bookings List */}
            <Box 
              maxH={{ base: "40vh", md: "50vh", lg: "52vh" }} 
              overflowY="auto"
              css={{
                '&::-webkit-scrollbar': {
                  width: '4px',
                },
                '&::-webkit-scrollbar-track': {
                  background: '#f1f1f1',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: '#888',
                  borderRadius: '4px',
                },
              }}
            >
              {allBookings.length > 0 ? (
                <VStack spacing={{ base: 2, md: 3 }} align="stretch">
                  {allBookings.map((booking, idx) => (
                    <Box
                      key={idx}
                      borderWidth="1px"
                      borderRadius="md"
                      overflow="hidden"
                      onClick={() => setSelectedBooking(booking)}
                      bg={booking === selectedBooking ? "blue.50" : "white"}
                      p={{ base: 3, md: 4 }}
                      cursor="pointer"
                      _hover={{ bg: "gray.50" }}
                      transition="background-color 0.2s"
                    >
                      <VStack align="stretch" spacing={2}>
                        <Text 
                          fontWeight="medium"
                          fontSize={{ base: "sm", md: "md" }}
                          noOfLines={2}
                        >
                          {booking.name.length > 50
                            ? `${booking.name.slice(0, 50)}...`
                            : booking.name}
                        </Text>
                        <HStack
                          fontSize={{ base: "xs", md: "sm" }}
                          color="gray.500"
                          spacing={1}
                        >
                          <CalendarIcon />
                          <Text>
                            {convertDateIntoIndianFormat(selectedDates[0])}
                            &nbsp; at &nbsp;
                            {`${convertTime(
                              booking?.startTime
                            )} - ${convertTime(booking?.endTime)}`}
                          </Text>
                        </HStack>
                        <HStack justify="flex-end" spacing={2}>
                          {booking.type === "class" && (
                            <Badge colorScheme="green" size={{ base: "sm", md: "md" }}>
                              Class
                            </Badge>
                          )}
                          {booking.type === "course" && (
                            <Badge colorScheme="yellow" size={{ base: "sm", md: "md" }}>
                              Course
                            </Badge>
                          )}
                          {booking.type === "event" && (
                            <Badge colorScheme="blue" size={{ base: "sm", md: "md" }}>
                              Break
                            </Badge>
                          )}
                        </HStack>
                      </VStack>
                    </Box>
                  ))}
                </VStack>
              ) : (
                <Text 
                  textAlign="center" 
                  color="gray.500"
                  py={{ base: 4, md: 8 }}
                  fontSize={{ base: "sm", md: "md" }}
                >
                  No Bookings Found
                </Text>
              )}
            </Box>
          </Box>

          {/* Right Panel - Booking Details */}
          <Box 
            flex={{ base: "1", lg: "0 0 50%" }}
            bg="white" 
            p={{ base: 4, md: 6 }} 
            borderRadius="md" 
            boxShadow="sm"
            minH={{ base: "300px", lg: "80vh" }}
          >
            {loading ? (
              <Stack spacing={4} py={{ base: 4, md: 10 }}>
                <Skeleton height="60px" borderRadius="md" />
                <SkeletonText noOfLines={3} spacing="4" skeletonHeight="2" />
                <Skeleton height="40px" borderRadius="md" />
                <SkeletonText noOfLines={2} spacing="4" skeletonHeight="2" />
              </Stack>
            ) : selectedBooking.name ? (
              <Stack spacing={{ base: 3, md: 4 }}>
                <Flex 
                  justify="space-between" 
                  align="flex-start"
                  direction={{ base: "column", md: "row" }}
                  gap={{ base: 3, md: 0 }}
                >
                  {/* Left - Image + Info */}
                  <HStack 
                    spacing={{ base: 3, md: 4 }}
                    align="flex-start"
                    flex="1"
                  >
                    <Image
                      boxSize={{ base: "50px", md: "70px" }}
                      objectFit="cover"
                      borderRadius="md"
                      src={selectedBooking.image}
                      alt="Course"
                    />
                    <VStack align="flex-start" spacing={1} flex="1">
                      <Text 
                        fontWeight="semibold" 
                        fontSize={{ base: "sm", md: "md" }}
                        noOfLines={2}
                      >
                        {selectedBooking.name}
                      </Text>
                      <Text 
                        fontWeight="medium" 
                        color="blue.600" 
                        fontSize={{ base: "xs", md: "sm" }}
                      >
                        {convertTime(selectedBooking.startTime)} -{" "}
                        {convertTime(selectedBooking.endTime)}
                      </Text>
                      <HStack
                        color="gray.600"
                        fontSize={{ base: "xs", md: "sm" }}
                        spacing={1}
                      >
                        <MdLocationOn />
                        <Text>{selectedBooking.facility}</Text>
                      </HStack>
                    </VStack>
                  </HStack>

                  {/* Right - Date, Count, Refresh */}
                  <VStack 
                    textAlign="right" 
                    fontSize={{ base: "xs", md: "sm" }}
                    spacing={2}
                    align={{ base: "stretch", md: "flex-end" }}
                  >
                    <Button
                      size={{ base: "xs", md: "sm" }}
                      variant="ghost"
                      colorScheme="green"
                      leftIcon={<RepeatIcon />}
                      onClick={getLatestBookings}
                    >
                      Refresh
                    </Button>
                    <Text>
                      {convertDateIntoIndianFormat(selectedDates[0])}
                    </Text>
                    <Text color="gray.500">
                      No. of Bookings: {selectedBooking?.bookings?.length || 0}
                    </Text>
                  </VStack>
                </Flex>

                {/* No bookings message */}
                <Box
                  borderTop="1px solid"
                  borderColor="gray.100"
                  pt={{ base: 2, md: 3 }}
                  textAlign="left"
                  fontSize={{ base: "xs", md: "sm" }}
                  color="gray.600"
                >
                  No Bookings Found
                </Box>
              </Stack>
            ) : (
              <Text 
                textAlign="center" 
                color="gray.500"
                py={{ base: 4, md: 8 }}
                fontSize={{ base: "sm", md: "md" }}
              >
                No Booking Selected
              </Text>
            )}
          </Box>
        </Flex>
      </Box>
    </Layout>
  );
};

export default Dashboard;